#!/usr/bin/env python3
"""
Debug favorite orders specific operations
"""

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_current_favorite_orders():
    """Check current state of favorite orders in Firebase"""
    print("📊 Checking Current Favorite Orders State...")
    
    try:
        from src.firebase_db import get_data, set_data, get_favorite_orders
        
        # Check raw Firebase data
        print("   🔍 Raw Firebase data:")
        raw_data = get_data("favorite_orders")
        print(f"      Raw data type: {type(raw_data)}")
        print(f"      Raw data: {raw_data}")
        
        # Check using get_favorite_orders function
        print("   🔍 Using get_favorite_orders function:")
        fav_data = get_favorite_orders()
        print(f"      Function result type: {type(fav_data)}")
        print(f"      Function result: {fav_data}")
        
        if fav_data:
            for user_id, orders in fav_data.items():
                print(f"      User {user_id}: {len(orders)} orders")
                for i, order in enumerate(orders):
                    print(f"         Order {i}: {order.get('favorite_name', 'No name')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking current state: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_save_operation():
    """Test saving a favorite order"""
    print("💾 Testing Save Operation...")
    
    try:
        from src.data_storage import save_favorite_order, get_user_favorite_orders
        from src.firebase_db import get_favorite_orders
        
        test_user_id = 123456
        test_order_data = {
            "restaurant_id": 1,  # Use integer instead of string
            "restaurant_name": "Test Restaurant",
            "items": [{"id": 1, "name": "Test Item", "price": 100, "quantity": 1}],
            "subtotal": 100,
            "delivery_fee": 25,
            "delivery_location": "Test Location",
            "restaurant_area_id": 1,
            "delivery_location_id": 1
        }
        
        print("   📝 Before save:")
        before_save = get_favorite_orders()
        print(f"      Users before: {len(before_save) if before_save else 0}")
        
        # Test save
        print("   💾 Saving favorite order...")
        save_result = save_favorite_order(test_user_id, "Debug Test Order", test_order_data)
        print(f"      Save result: {save_result}")
        
        print("   📖 After save:")
        after_save = get_favorite_orders()
        print(f"      Users after: {len(after_save) if after_save else 0}")
        
        # Check local cache
        local_favorites = get_user_favorite_orders(test_user_id, sync_data=False)
        print(f"      Local cache: {len(local_favorites)} orders")
        
        # Check Firebase directly
        user_firebase_data = get_favorite_orders(str(test_user_id))
        print(f"      Firebase data: {user_firebase_data}")
        
        return save_result
        
    except Exception as e:
        print(f"   ❌ Error in save operation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clear_operation():
    """Test clearing favorite orders"""
    print("🗑️ Testing Clear Operation...")
    
    try:
        from src.firebase_db import clear_all_favorite_orders, get_favorite_orders
        
        print("   📊 Before clear:")
        before_clear = get_favorite_orders()
        print(f"      Users before: {len(before_clear) if before_clear else 0}")
        if before_clear:
            total_orders = sum(len(orders) for orders in before_clear.values())
            print(f"      Total orders before: {total_orders}")
        
        # Test clear
        print("   🗑️ Clearing favorite orders...")
        clear_result = clear_all_favorite_orders()
        print(f"      Clear result: {clear_result}")
        
        print("   📊 After clear:")
        after_clear = get_favorite_orders()
        print(f"      Users after: {len(after_clear) if after_clear else 0}")
        if after_clear:
            total_orders = sum(len(orders) for orders in after_clear.values())
            print(f"      Total orders after: {total_orders}")
        
        return clear_result
        
    except Exception as e:
        print(f"   ❌ Error in clear operation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_storage_loading():
    """Test data storage loading process"""
    print("🔄 Testing Data Storage Loading...")
    
    try:
        from src.data_storage import favorite_orders
        from src.firebase_db import get_favorite_orders
        
        print("   📊 Current local cache:")
        print(f"      Local cache users: {len(favorite_orders)}")
        if favorite_orders:
            total_local = sum(len(orders) for orders in favorite_orders.values())
            print(f"      Total local orders: {total_local}")
            for user_id, orders in favorite_orders.items():
                print(f"         User {user_id}: {len(orders)} orders")
        
        print("   📊 Current Firebase data:")
        firebase_data = get_favorite_orders()
        print(f"      Firebase users: {len(firebase_data) if firebase_data else 0}")
        if firebase_data:
            total_firebase = sum(len(orders) for orders in firebase_data.values())
            print(f"      Total Firebase orders: {total_firebase}")
            for user_id, orders in firebase_data.items():
                print(f"         User {user_id}: {len(orders)} orders")
        
        # Test manual sync
        print("   🔄 Manual sync test:")
        favorite_orders.clear()
        favorite_orders.update(firebase_data or {})
        print(f"      After manual sync - local users: {len(favorite_orders)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error in data storage loading: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run specific favorite orders debug tests"""
    print("🔍 Favorite Orders Specific Debug Suite")
    print("=" * 60)
    
    tests = [
        ("Current State Check", test_current_favorite_orders),
        ("Save Operation", test_save_operation),
        ("Data Storage Loading", test_data_storage_loading),
        ("Clear Operation", test_clear_operation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
        except Exception as e:
            print(f"❌ CRASH - {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEBUG SUMMARY")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")

if __name__ == "__main__":
    main()
