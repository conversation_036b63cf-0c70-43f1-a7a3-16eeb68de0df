#!/usr/bin/env python3
"""
Debug script for Firebase favorite orders operations
"""

import sys
import os
import json
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_firebase_connection():
    """Test basic Firebase connection"""
    print("🔥 Testing Firebase Connection...")
    try:
        from src.firebase_db import initialize_firebase, get_data, set_data
        
        # Test initialization
        init_result = initialize_firebase()
        print(f"   Firebase initialization: {init_result}")
        
        # Test basic read operation
        test_data = get_data("test_connection")
        print(f"   Test read operation: {test_data is not None}")
        
        # Test basic write operation
        test_write_data = {"test": "connection", "timestamp": datetime.now().isoformat()}
        write_result = set_data("test_connection", test_write_data)
        print(f"   Test write operation: {write_result}")
        
        # Verify write by reading back
        read_back = get_data("test_connection")
        print(f"   Write verification: {read_back is not None and read_back.get('test') == 'connection'}")
        
        return init_result and write_result
        
    except Exception as e:
        print(f"   ❌ Firebase connection test failed: {e}")
        return False

def test_favorite_orders_operations():
    """Test favorite orders specific operations"""
    print("⭐ Testing Favorite Orders Operations...")
    try:
        from src.firebase_db import (
            get_favorite_orders, 
            update_favorite_orders, 
            clear_all_favorite_orders,
            set_data,
            get_data
        )
        
        test_user_id = "test_user_999"
        test_order = {
            "favorite_name": "Debug Test Order",
            "restaurant_id": "restaurant_1",
            "items": [{"id": 1, "name": "Test Item", "price": 100, "quantity": 1}],
            "subtotal": 100,
            "delivery_fee": 25,
            "timestamp": datetime.now().isoformat()
        }
        
        # Test 1: Direct Firebase write
        print("   📝 Test 1: Direct Firebase write")
        direct_write = set_data(f"favorite_orders/{test_user_id}", [test_order])
        print(f"      Direct write result: {direct_write}")
        
        # Test 2: Direct Firebase read
        print("   📖 Test 2: Direct Firebase read")
        direct_read = get_data(f"favorite_orders/{test_user_id}")
        print(f"      Direct read result: {direct_read is not None}")
        if direct_read:
            print(f"      Read data length: {len(direct_read) if isinstance(direct_read, list) else 'Not a list'}")
        
        # Test 3: Using favorite orders functions
        print("   🔧 Test 3: Using favorite orders functions")
        update_result = update_favorite_orders(test_user_id, [test_order])
        print(f"      Update function result: {update_result}")
        
        get_result = get_favorite_orders(test_user_id)
        print(f"      Get function result: {get_result is not None}")
        if get_result:
            print(f"      Retrieved users: {list(get_result.keys())}")
            print(f"      Retrieved orders count: {len(get_result.get(test_user_id, []))}")
        
        # Test 4: Get all favorite orders
        print("   📋 Test 4: Get all favorite orders")
        all_favorites = get_favorite_orders()
        print(f"      All favorites result: {all_favorites is not None}")
        if all_favorites:
            print(f"      Total users with favorites: {len(all_favorites)}")
            total_orders = sum(len(user_fav) for user_fav in all_favorites.values())
            print(f"      Total favorite orders: {total_orders}")
        
        # Test 5: Clear operation
        print("   🗑️ Test 5: Clear operation")
        clear_result = clear_all_favorite_orders()
        print(f"      Clear result: {clear_result}")
        
        # Verify clear
        after_clear = get_favorite_orders()
        print(f"      After clear - users: {len(after_clear) if after_clear else 0}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Favorite orders operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_storage_functions():
    """Test data storage layer functions"""
    print("💾 Testing Data Storage Functions...")
    try:
        from src.data_storage import (
            save_favorite_order,
            get_user_favorite_orders,
            delete_favorite_order,
            validate_favorite_orders_persistence
        )
        
        test_user_id = 888888
        test_order_data = {
            "restaurant_id": "restaurant_1",
            "items": [{"id": 1, "name": "Storage Test Item", "price": 150, "quantity": 2}],
            "subtotal": 300,
            "delivery_fee": 30,
            "restaurant_name": "Test Restaurant"
        }
        
        # Test 1: Save favorite order
        print("   💾 Test 1: Save favorite order")
        save_result = save_favorite_order(test_user_id, "Storage Test Order", test_order_data)
        print(f"      Save result: {save_result}")
        
        # Test 2: Get favorite orders
        print("   📖 Test 2: Get favorite orders")
        user_favorites = get_user_favorite_orders(test_user_id, sync_data=False)
        print(f"      Get result: {len(user_favorites)} orders found")
        
        # Test 3: Validate persistence
        print("   🔍 Test 3: Validate persistence")
        validation_result = validate_favorite_orders_persistence()
        print(f"      Validation result: {validation_result['valid']}")
        print(f"      Local: {validation_result['local_users']} users, {validation_result['local_orders']} orders")
        print(f"      Firebase: {validation_result['firebase_users']} users, {validation_result['firebase_orders']} orders")
        
        # Test 4: Delete favorite order
        if len(user_favorites) > 0:
            print("   🗑️ Test 4: Delete favorite order")
            delete_result = delete_favorite_order(test_user_id, 0)
            print(f"      Delete result: {delete_result}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data storage functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reset_functionality():
    """Test the reset functionality"""
    print("🔄 Testing Reset Functionality...")
    try:
        from src.utils.favorite_orders_sync import clear_all_favorite_orders_with_backup
        from src.firebase_db import get_favorite_orders, update_favorite_orders
        
        # First, add some test data
        print("   📝 Adding test data...")
        test_data = {
            "test_user_1": [{
                "favorite_name": "Reset Test 1",
                "restaurant_id": "restaurant_1",
                "items": [{"id": 1, "name": "Test Item 1", "price": 100}],
                "subtotal": 100
            }],
            "test_user_2": [{
                "favorite_name": "Reset Test 2", 
                "restaurant_id": "restaurant_2",
                "items": [{"id": 2, "name": "Test Item 2", "price": 200}],
                "subtotal": 200
            }]
        }
        
        # Add test data
        for user_id, orders in test_data.items():
            update_result = update_favorite_orders(user_id, orders)
            print(f"      Added test data for {user_id}: {update_result}")
        
        # Verify test data exists
        print("   📊 Verifying test data...")
        before_reset = get_favorite_orders()
        print(f"      Before reset - users: {len(before_reset) if before_reset else 0}")
        
        # Test reset
        print("   🗑️ Testing reset...")
        reset_result = clear_all_favorite_orders_with_backup()
        print(f"      Reset result: {reset_result['success']}")
        if reset_result['success']:
            print(f"      Orders cleared: {reset_result['orders_cleared']}")
            print(f"      Backup created: {reset_result['backup_created']}")
        else:
            print(f"      Reset error: {reset_result.get('error', 'Unknown')}")
        
        # Verify reset
        print("   ✅ Verifying reset...")
        after_reset = get_favorite_orders()
        print(f"      After reset - users: {len(after_reset) if after_reset else 0}")
        
        return reset_result['success']
        
    except Exception as e:
        print(f"   ❌ Reset functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all debug tests"""
    print("🔍 Firebase Favorite Orders Debug Suite")
    print("=" * 60)
    
    tests = [
        ("Firebase Connection", test_firebase_connection),
        ("Favorite Orders Operations", test_favorite_orders_operations),
        ("Data Storage Functions", test_data_storage_functions),
        ("Reset Functionality", test_reset_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
        except Exception as e:
            print(f"❌ CRASH - {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEBUG SUMMARY")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")

if __name__ == "__main__":
    main()
