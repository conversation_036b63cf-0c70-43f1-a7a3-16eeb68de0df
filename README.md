# ⚠️ Proprietary Notice

This project is proprietary and confidential. No part of this project may be copied, distributed, or used without explicit written permission from the author. See LICENSE for details.

<div align="center">

# 🍽️ Wiz Aroma Food Delivery System

### *Intelligent Food Delivery Management Platform*

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Telegram Bot API](https://img.shields.io/badge/Telegram-Bot%20API-blue.svg)](https://core.telegram.org/bots/api)
[![Firebase](https://img.shields.io/badge/Firebase-Realtime%20DB-orange.svg)](https://firebase.google.com)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-2.0-brightgreen.svg)](https://github.com/Mih-Nig-Afe/Wiz-Aroma-Food-Delivery)

*A comprehensive multi-bot Telegram-based food delivery system with advanced automation capabilities*

[🚀 Features](#-current-features) • [📋 Documentation](#-documentation) • [🔧 Installation](#-installation--setup) • [🎯 Roadmap](#-release-notes--roadmap)

</div>

---

## 🎯 **Project Overview**

Wiz Aroma Food Delivery is an advanced multi-bot Telegram delivery system currently serving **50-120 orders daily** with **enterprise-grade security** and **production-ready automation**. The system features a sophisticated architecture with specialized bots for different operational functions and comprehensive security hardening.

## 🚀 **Current Features**

### 👥 **Customer Experience**

- 🏪 **Multi-Restaurant Selection**: Browse restaurants by geographical area
- 📱 **Smart Menu System**: Intuitive categorized interface with real-time pricing
- 💫 **Points Reward System**: Earn 11% of delivery fee as loyalty points
- 💳 **Multiple Payment Methods**: Telebirr, CBE Bank, BOA Bank, Points redemption
- ⭐ **Favorite Orders**: One-click reordering of preferred meals
- 📍 **Delivery Tracking**: Basic order status updates
- ⏰ **Operating Hours**: Flexable working hours for weekdays and weekends

### 🤖 **Multi-Bot Architecture**

- **🛍️ User Bot** : Customer ordering interface
- **📊 Management Bot**: Comprehensive analytics, personnel management, and data operations
- **🚚 Delivery Bot**: Real-time order assignment and delivery coordination
- **🔍 Audit Bot**: System monitoring and performance tracking

### 🗄️ **Data Management System**

- **🔄 Seasonal Data Reset**: Complete system refresh while preserving critical data
- **🧹 Daily Order Cleanup**: Automated maintenance for optimal performance
- **📋 Audit Logging**: Comprehensive operation tracking and security
- **💾 Backup & Recovery**: Automated data archiving with 24-hour recovery window
- **🔐 Access Control**: Multi-layer authorization for sensitive operations
- **📊 Zero-Data Handling**: Graceful analytics display for fresh system states
- **👨‍💼 Admin Bot** : Order management and oversight
- **💰 Finance Bot** : Payment verification (currently manual)
- **🔧 Maintenance Bot** : Area,Restaurant,Meal,Price, Delivery location configurations
- **👨‍💻 Management Bot** : Managerial system to do the Hire, Analysis, Report, Payroll and other management fetchers
- **🚚 Order Track Bot** : Order Tracking bot to check the status of the order

### 🛠️ **Technical Features**

- **🔥 Firebase Integration**: Real-time database with local backup
- **🔒 Security**: Role-based access control and secure credential management
- **📊 Logging**: Comprehensive audit trails and error tracking
- **⚡ Performance**: 99.5% uptime with advanced monitoring and optimization
- **🔄 Data Sync**: Automatic synchronization between cloud and local storage

## 📱 **Quick Start Guide**

### 🛍️ **For Customers**

1. **Start Ordering**: Send `/start` to **User Bot**
2. **Main Menu Options**:
   - 🍽️ **Order Food** - Browse and order from restaurants
   - 💫 **My Points** - Check loyalty points balance
   - ⭐ **My Favorites** - Quick reorder saved meals
   - ℹ️ **Help** - Get assistance and support

3. **Ordering Process**:

   ```
   Select Area → Choose Restaurant → Add Items →
   Delivery Details → Payment → Verification → Delivery
   ```

### 👨‍💼 **For Administrators**

- **Order Management**: Review and approve/reject incoming orders
- **Customer Communication**: Direct messaging with customers
- **System Monitoring**: Track order flow and system performance

## 🔧 **Installation & Setup**

### Prerequisites

```bash
Python 3.9+
Firebase Account
Telegram Bot Tokens
```

### Quick Installation

```bash
# Clone the repository
git clone https://github.com/Mih-Nig-Afe/Wiz-Aroma-Food-Delivery.git
cd Wiz-Aroma-Food-Delivery

# Install dependencies
pip install -r requirements.txt

# Configure environment variables
cp .env.example .env
# Edit .env with your bot tokens and Firebase credentials

# Run the system
python main.py
```

## 📋 **Documentation**

### 🛠️ **Technical Documentation**

- **[SYSTEM_ARCHITECTURE.md](SYSTEM_ARCHITECTURE.md)** - System architecture overview
- **[PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)** - Project structure details
- **[FIREBASE_SETUP.md](FIREBASE_SETUP.md)** - Firebase integration guide
- **[DEPLOYMENT.md](DEPLOYMENT.md)** - Deployment instructions
- **[SECURITY.md](SECURITY.md)** - Security best practices

## 🔒 **Security & Production Features**

### **🛡️ Enterprise-Grade Security**

- **🔐 Zero Hardcoded Credentials:** All sensitive data externalized to environment variables
- **🚫 GitHub-Safe Repository:** No production credentials exposed in source code
- **🔑 Dynamic Authorization:** Real-time access control via Firebase Firestore
- **📊 Comprehensive Audit Logging:** All admin actions tracked with timestamps
- **⚡ Rate Limiting:** Admin actions limited to prevent abuse (10/minute)
- **🚨 Error Reporting:** Critical errors automatically reported to administrators
- **✅ Graceful Error Handling:** Robust fallback mechanisms for all operations
- **🔄 Environment Validation:** System exits safely if required credentials missing

### **🎯 Access Control**

- **Admin Management:** Only authorized Telegram IDs can access administrative features
- **Role-Based Permissions:** Different access levels for different bot functions
- **Secure Fallbacks:** No hardcoded credentials in any fallback mechanisms
- **Production Ready:** Safe for public GitHub hosting and open-source distribution

## 📊 **Release Notes & Roadmap**

- For the latest release details, see [RELEASE_NOTES_V2.0.md](https://github.com/Mih-Nig-Afe/Wiz-Aroma-Food-Delivery/releases/tag/V.2.0).

### **Version Roadmap**

| Version | Status      | Key Features                                 | Target Date |
|---------|-------------|-----------------------------------------------|-------------|
| V1.3.3  | Released    | Multi-bot system, Firebase integration        | July 2025   |
| V2.0    | Current     | Enterprise security, credential elimination   | August 2025 |
| V3.0    | Planned     | AI automation, intelligent distribution       | Q4 2025     |
| V4.0    | Future      | Mobile app, multi-language support            | Q1 2026     |

---

<div align="center">

### 🌟 **Star this repository if you find it helpful!**

**Made by [Mihretab Nigatu](https://github.com/Mih-Nig-Afe)**

*Transforming food delivery through intelligent automation*

</div>
