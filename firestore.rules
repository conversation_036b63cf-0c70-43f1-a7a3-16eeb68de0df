rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions for security
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.uid in ['admin_uid_1', 'admin_uid_2']; // Replace with actual admin UIDs
    }
    
    function isAuthorizedBot() {
      return isAuthenticated() && 
             request.auth.token.bot_authorized == true;
    }
    
    function isValidData(data) {
      return data.keys().hasAll(['timestamp']) &&
             data.timestamp is timestamp;
    }
    
    // System health monitoring (read-only for monitoring)
    match /system_health/{document} {
      allow read: if isAuthenticated();
      allow write: if isAuthorizedBot();
    }
    
    // User data collections - restricted access
    match /user_points/{userId} {
      allow read, write: if isAuthorizedBot() && 
                           resource == null || 
                           resource.data.user_id == userId;
    }
    
    match /user_names/{userId} {
      allow read, write: if isAuthorizedBot() && 
                           resource == null || 
                           resource.data.user_id == userId;
    }
    
    match /user_phone_numbers/{userId} {
      allow read, write: if isAuthorizedBot() && 
                           resource == null || 
                           resource.data.user_id == userId;
    }
    
    // Delivery personnel management - admin only
    match /delivery_personnel/{personnelId} {
      allow read: if isAuthorizedBot();
      allow write: if isAdmin() || isAuthorizedBot();
      allow create: if isAdmin() && isValidData(request.resource.data);
      allow update: if isAdmin() && isValidData(request.resource.data);
      allow delete: if isAdmin();
    }
    
    match /authorized_delivery_personnel/{personnelId} {
      allow read: if isAuthorizedBot();
      allow write: if isAdmin();
      allow create: if isAdmin() && isValidData(request.resource.data);
      allow update: if isAdmin() && isValidData(request.resource.data);
      allow delete: if isAdmin();
    }
    
    // Delivery personnel operational data
    match /delivery_personnel_availability/{personnelId} {
      allow read, write: if isAuthorizedBot();
    }
    
    match /delivery_personnel_capacity/{personnelId} {
      allow read, write: if isAuthorizedBot();
    }
    
    match /delivery_personnel_capacity_tracking/{personnelId} {
      allow read, write: if isAuthorizedBot();
    }
    
    match /delivery_personnel_zones/{personnelId} {
      allow read, write: if isAuthorizedBot();
    }
    
    match /delivery_personnel_performance/{personnelId} {
      allow read: if isAuthorizedBot();
      allow write: if isAdmin() || isAuthorizedBot();
    }
    
    match /delivery_personnel_earnings/{personnelId} {
      allow read: if isAuthorizedBot();
      allow write: if isAdmin() || isAuthorizedBot();
    }
    
    match /delivery_personnel_assignments/{assignmentId} {
      allow read, write: if isAuthorizedBot();
    }
    
    match /delivery_personnel_availability_log/{logId} {
      allow read: if isAuthorizedBot();
      allow write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    // Order management
    match /confirmed_orders/{orderId} {
      allow read, write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    match /completed_orders/{orderId} {
      allow read: if isAuthorizedBot();
      allow write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    match /order_broadcast_messages/{orderId} {
      allow read, write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    match /order_broadcast_metadata/{orderId} {
      allow read, write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    // Audit and logging - admin read access
    match /authorization_audit_log/{logId} {
      allow read: if isAdmin();
      allow write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    match /management_operations_log/{logId} {
      allow read: if isAdmin();
      allow write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    // Analytics and reporting data - admin access
    match /analytics_data/{document} {
      allow read: if isAdmin() || isAuthorizedBot();
      allow write: if isAuthorizedBot();
    }
    
    match /financial_reports/{reportId} {
      allow read: if isAdmin();
      allow write: if isAuthorizedBot();
      allow create: if isAuthorizedBot() && isValidData(request.resource.data);
    }
    
    // Configuration data - admin only write
    match /system_config/{configId} {
      allow read: if isAuthorizedBot();
      allow write: if isAdmin();
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
