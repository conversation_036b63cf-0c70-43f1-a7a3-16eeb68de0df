#!/usr/bin/env python3
"""
Firebase Database Initialization Script for Wiz-Aroma Demo
This script initializes a new Firebase database with sample data for presentation purposes.
"""

import os
import sys
import logging
from datetime import datetime

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded environment variables from .env file")
except ImportError:
    print("⚠️  Warning: python-dotenv not installed. Assuming environment variables are already set.")

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'firebase_init_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main function to initialize Firebase database with sample data"""
    print("🚀 Wiz-Aroma Firebase Database Initialization")
    print("=" * 50)
    print("This script will initialize your Firebase database with sample data for demo purposes.")
    print()
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ Error: .env file not found!")
        print("Please ensure your .env file exists with proper Firebase configuration.")
        return False
    
    try:
        # Import Firebase functions
        from firebase_db import initialize_firebase, initialize_sample_data, test_firebase_connectivity
        
        print("📋 Step 1: Testing Firebase connectivity...")
        if not test_firebase_connectivity():
            print("❌ Firebase connectivity test failed!")
            print("Please check your Firebase configuration in .env file:")
            print("- FIREBASE_DATABASE_URL")
            print("- FIREBASE_CREDENTIALS")
            return False
        
        print("✅ Firebase connectivity test passed!")
        print()
        
        print("📋 Step 2: Initializing Firebase database with sample data...")
        print("This will create the following collections:")
        print("- areas (3 Sample Areas)")
        print("- restaurants (6 Test Restaurants)")
        print("- menus (14 Sample Menu Items)")
        print("- delivery_locations (5 Test Locations)")
        print("- delivery_fees (15 Sample Delivery Fees)")
        print("- All required empty user data collections")
        print()
        
        # Ask for confirmation
        response = input("Do you want to proceed? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Initialization cancelled by user.")
            return False
        
        print("🔄 Testing database write permissions...")

        # Test if we can write to the database at all
        try:
            from firebase_db import db
            test_ref = db.reference("test_write")
            test_ref.set({"test": True, "timestamp": "2025-08-02"})
            print("✅ Database write test successful!")

            # Clean up test data
            test_ref.delete()
            print("✅ Database delete test successful!")

        except Exception as e:
            print(f"❌ Database write test failed: {e}")
            print()
            print("🔧 SOLUTION: Your Firebase Realtime Database security rules may be blocking writes.")
            print("Please go to your Firebase Console:")
            print("1. Open https://console.firebase.google.com/")
            print("2. Select your project: wiz-aroma-testing")
            print("3. Go to 'Realtime Database' in the left menu")
            print("4. Click on the 'Rules' tab")
            print("5. Temporarily set rules to allow all reads/writes:")
            print("   {")
            print('     "rules": {')
            print('       ".read": true,')
            print('       ".write": true')
            print("     }")
            print("   }")
            print("6. Click 'Publish'")
            print()
            print("⚠️  Remember to secure your rules after initialization!")
            return False

        print("🔄 Initializing database...")
        if initialize_sample_data():
            print()
            print("🎉 SUCCESS! Firebase database has been initialized with sample data.")
            print()
            print("📊 Sample Data Summary:")
            print("- Sample Area 1, Sample Area 2, Sample Area 3")
            print("- Test Restaurant 1-6 (2 per area)")
            print("- Sample Menu Item 1-14 (distributed across restaurants)")
            print("- Test Location 1-5")
            print("- Delivery fees ranging from 25-45 ETB")
            print()
            print("✅ You can now run your Wiz-Aroma system with: python main.py --bot all")
            return True
        else:
            print("❌ Failed to initialize Firebase database with sample data.")
            print("Please check the logs for more details.")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all required dependencies are installed.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        logger.error(f"Unexpected error during initialization: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
