"""
Favorite Orders Synchronization Utilities for Wiz Aroma Food Delivery System

Handles automatic synchronization of favorite orders when underlying data changes:
- Updates meal prices when they change in the database
- Updates meal names when they are renamed
- Removes meals from favorites when they are deleted
- Updates delivery fees and other order-related data dynamically
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from src.config import logger
from src.firebase_db import (
    get_favorite_orders,
    update_favorite_orders,
    get_data,
    clear_all_favorite_orders
)
from src.data_storage import (
    get_restaurant_menu,
    get_delivery_fee,
    get_area_by_id,
    get_delivery_location_by_id,
    get_restaurant_by_id
)


def sync_favorite_order_meal_data(favorite_order: Dict[str, Any]) -> <PERSON><PERSON>[Dict[str, Any], bool]:
    """
    Synchronize a single favorite order's meal data with current database values.
    
    Args:
        favorite_order: The favorite order to synchronize
        
    Returns:
        Tuple of (updated_order, has_changes)
    """
    try:
        updated_order = favorite_order.copy()
        has_changes = False
        
        # Get restaurant ID from the order
        restaurant_id = updated_order.get("restaurant_id")
        if not restaurant_id:
            logger.warning("Favorite order missing restaurant_id, skipping sync")
            return updated_order, False
            
        # Get current menu for the restaurant
        current_menu = get_restaurant_menu(restaurant_id)
        if not current_menu:
            logger.warning(f"No menu found for restaurant {restaurant_id}, skipping sync")
            return updated_order, False
            
        # Create a lookup dictionary for current menu items
        menu_lookup = {item["id"]: item for item in current_menu}
        
        # Update items in the favorite order
        updated_items = []
        items_to_remove = []
        
        for item in updated_order.get("items", []):
            item_id = item.get("id")
            if not item_id:
                # Item without ID, keep as is
                updated_items.append(item)
                continue
                
            # Check if item still exists in menu
            if item_id not in menu_lookup:
                # Item was deleted from menu, mark for removal
                items_to_remove.append(item)
                has_changes = True
                logger.info(f"Removing deleted menu item {item_id} from favorite order")
                continue
                
            current_item = menu_lookup[item_id]
            updated_item = item.copy()
            
            # Update price if changed
            if item.get("price") != current_item["price"]:
                old_price = item.get("price", 0)
                updated_item["price"] = current_item["price"]
                has_changes = True
                logger.info(f"Updated price for item {item_id}: {old_price} -> {current_item['price']}")
                
            # Update name if changed
            if item.get("name") != current_item["name"]:
                old_name = item.get("name", "Unknown")
                updated_item["name"] = current_item["name"]
                has_changes = True
                logger.info(f"Updated name for item {item_id}: '{old_name}' -> '{current_item['name']}'")
                
            updated_items.append(updated_item)
            
        # Update the items list
        updated_order["items"] = updated_items
        
        # Recalculate subtotal if items changed
        if has_changes:
            new_subtotal = sum(
                item.get("price", 0) * item.get("quantity", 1) 
                for item in updated_items
            )
            if new_subtotal != updated_order.get("subtotal", 0):
                updated_order["subtotal"] = new_subtotal
                logger.info(f"Updated subtotal: {updated_order.get('subtotal', 0)} -> {new_subtotal}")
                
        return updated_order, has_changes
        
    except Exception as e:
        logger.error(f"Error syncing favorite order meal data: {e}")
        return favorite_order, False


def sync_favorite_order_delivery_data(favorite_order: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
    """
    Synchronize a single favorite order's delivery data with current database values.
    
    Args:
        favorite_order: The favorite order to synchronize
        
    Returns:
        Tuple of (updated_order, has_changes)
    """
    try:
        updated_order = favorite_order.copy()
        has_changes = False
        
        # Get delivery location and area data
        restaurant_area_id = updated_order.get("restaurant_area_id")
        delivery_location_id = updated_order.get("delivery_location_id")
        
        if not restaurant_area_id or not delivery_location_id:
            logger.debug("Favorite order missing area or location ID, skipping delivery sync")
            return updated_order, False
            
        # Update delivery fee if changed
        try:
            current_delivery_fee = get_delivery_fee(restaurant_area_id, delivery_location_id)
            old_delivery_fee = updated_order.get("delivery_fee", 0)
            
            if current_delivery_fee != old_delivery_fee and current_delivery_fee > 0:
                updated_order["delivery_fee"] = current_delivery_fee
                has_changes = True
                logger.info(f"Updated delivery fee: {old_delivery_fee} -> {current_delivery_fee}")
                
        except Exception as e:
            logger.warning(f"Could not update delivery fee for favorite order: {e}")
            
        # Update area name if available
        try:
            area = get_area_by_id(restaurant_area_id)
            if area and area.get("name"):
                old_area_name = updated_order.get("restaurant_area_name", "")
                new_area_name = area["name"]
                if old_area_name != new_area_name:
                    updated_order["restaurant_area_name"] = new_area_name
                    has_changes = True
                    logger.info(f"Updated area name: '{old_area_name}' -> '{new_area_name}'")
        except Exception as e:
            logger.warning(f"Could not update area name for favorite order: {e}")
            
        # Update delivery location name if available
        try:
            location = get_delivery_location_by_id(delivery_location_id)
            if location and location.get("name"):
                old_location_name = updated_order.get("delivery_location_name", "")
                new_location_name = location["name"]
                if old_location_name != new_location_name:
                    updated_order["delivery_location_name"] = new_location_name
                    has_changes = True
                    logger.info(f"Updated location name: '{old_location_name}' -> '{new_location_name}'")
        except Exception as e:
            logger.warning(f"Could not update location name for favorite order: {e}")
            
        # Update restaurant name if available
        try:
            restaurant_id = updated_order.get("restaurant_id")
            if restaurant_id:
                restaurant = get_restaurant_by_id(restaurant_id)
                if restaurant and restaurant.get("name"):
                    old_restaurant_name = updated_order.get("restaurant_name", "")
                    new_restaurant_name = restaurant["name"]
                    if old_restaurant_name != new_restaurant_name:
                        updated_order["restaurant_name"] = new_restaurant_name
                        has_changes = True
                        logger.info(f"Updated restaurant name: '{old_restaurant_name}' -> '{new_restaurant_name}'")
        except Exception as e:
            logger.warning(f"Could not update restaurant name for favorite order: {e}")
            
        return updated_order, has_changes
        
    except Exception as e:
        logger.error(f"Error syncing favorite order delivery data: {e}")
        return favorite_order, False


def sync_single_favorite_order(favorite_order: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
    """
    Synchronize a single favorite order with current database values.
    
    Args:
        favorite_order: The favorite order to synchronize
        
    Returns:
        Tuple of (updated_order, has_changes)
    """
    try:
        # Start with the original order
        updated_order = favorite_order.copy()
        total_changes = False
        
        # Sync meal data
        updated_order, meal_changes = sync_favorite_order_meal_data(updated_order)
        total_changes = total_changes or meal_changes
        
        # Sync delivery data
        updated_order, delivery_changes = sync_favorite_order_delivery_data(updated_order)
        total_changes = total_changes or delivery_changes
        
        # Update last sync timestamp if changes were made
        if total_changes:
            updated_order["last_synced"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
        return updated_order, total_changes
        
    except Exception as e:
        logger.error(f"Error syncing favorite order: {e}")
        return favorite_order, False


def sync_user_favorite_orders(user_id: str) -> bool:
    """
    Synchronize all favorite orders for a specific user.
    
    Args:
        user_id: The user ID to sync favorite orders for
        
    Returns:
        True if sync was successful, False otherwise
    """
    try:
        # Get user's favorite orders
        user_favorites_data = get_favorite_orders(user_id)
        user_favorites = user_favorites_data.get(user_id, [])
        
        if not user_favorites:
            logger.debug(f"No favorite orders found for user {user_id}")
            return True
            
        updated_favorites = []
        total_changes = False
        
        for favorite_order in user_favorites:
            updated_order, has_changes = sync_single_favorite_order(favorite_order)
            updated_favorites.append(updated_order)
            total_changes = total_changes or has_changes
            
        # Save updated favorites if changes were made
        if total_changes:
            success = update_favorite_orders(user_id, updated_favorites)
            if success:
                logger.info(f"Successfully synced favorite orders for user {user_id}")
            else:
                logger.error(f"Failed to save synced favorite orders for user {user_id}")
            return success
        else:
            logger.debug(f"No changes needed for user {user_id} favorite orders")
            return True
            
    except Exception as e:
        logger.error(f"Error syncing favorite orders for user {user_id}: {e}")
        return False


def sync_all_favorite_orders() -> Dict[str, Any]:
    """
    Synchronize all favorite orders in the system.
    
    Returns:
        Dictionary with sync results and statistics
    """
    try:
        logger.info("🔄 Starting global favorite orders synchronization...")
        
        # Get all favorite orders
        all_favorites = get_favorite_orders()
        
        if not all_favorites:
            logger.info("No favorite orders found to sync")
            return {
                "success": True,
                "users_processed": 0,
                "orders_updated": 0,
                "errors": []
            }
            
        users_processed = 0
        orders_updated = 0
        errors = []
        
        for user_id, user_favorites in all_favorites.items():
            try:
                users_processed += 1
                
                updated_favorites = []
                user_changes = False
                
                for favorite_order in user_favorites:
                    updated_order, has_changes = sync_single_favorite_order(favorite_order)
                    updated_favorites.append(updated_order)
                    
                    if has_changes:
                        orders_updated += 1
                        user_changes = True
                        
                # Save updated favorites if changes were made
                if user_changes:
                    success = update_favorite_orders(user_id, updated_favorites)
                    if not success:
                        error_msg = f"Failed to save synced orders for user {user_id}"
                        errors.append(error_msg)
                        logger.error(error_msg)
                        
            except Exception as e:
                error_msg = f"Error syncing orders for user {user_id}: {e}"
                errors.append(error_msg)
                logger.error(error_msg)
                
        logger.info(f"✅ Favorite orders sync completed: {users_processed} users, {orders_updated} orders updated")
        
        return {
            "success": len(errors) == 0,
            "users_processed": users_processed,
            "orders_updated": orders_updated,
            "errors": errors
        }
        
    except Exception as e:
        error_msg = f"Error in global favorite orders sync: {e}"
        logger.error(error_msg)
        return {
            "success": False,
            "users_processed": 0,
            "orders_updated": 0,
            "errors": [error_msg]
        }


def clear_all_favorite_orders_with_backup() -> Dict[str, Any]:
    """
    Clear all favorite orders with backup creation.
    
    Returns:
        Dictionary with operation results
    """
    try:
        logger.info("🗑️ Starting favorite orders reset with backup...")
        
        # Get current favorite orders for backup
        all_favorites = get_favorite_orders()
        
        if not all_favorites:
            logger.info("No favorite orders to clear")
            return {
                "success": True,
                "backup_created": False,
                "orders_cleared": 0
            }
            
        # Count total orders for reporting
        total_orders = sum(len(user_favorites) for user_favorites in all_favorites.values())
        
        # Create backup with timestamp
        backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"favorite_orders_backup_{backup_timestamp}"
        
        try:
            from src.firebase_db import set_data
            backup_success = set_data(backup_path, all_favorites)
            if not backup_success:
                logger.error("Failed to create backup, aborting reset")
                return {
                    "success": False,
                    "backup_created": False,
                    "orders_cleared": 0,
                    "error": "Backup creation failed"
                }
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            return {
                "success": False,
                "backup_created": False,
                "orders_cleared": 0,
                "error": f"Backup creation failed: {e}"
            }
            
        # Clear all favorite orders
        clear_success = clear_all_favorite_orders()
        
        if clear_success:
            logger.info(f"✅ Successfully cleared {total_orders} favorite orders from {len(all_favorites)} users")
            return {
                "success": True,
                "backup_created": True,
                "backup_path": backup_path,
                "orders_cleared": total_orders,
                "users_affected": len(all_favorites)
            }
        else:
            logger.error("Failed to clear favorite orders")
            return {
                "success": False,
                "backup_created": True,
                "backup_path": backup_path,
                "orders_cleared": 0,
                "error": "Clear operation failed"
            }
            
    except Exception as e:
        error_msg = f"Error in favorite orders reset: {e}"
        logger.error(error_msg)
        return {
            "success": False,
            "backup_created": False,
            "orders_cleared": 0,
            "error": error_msg
        }
