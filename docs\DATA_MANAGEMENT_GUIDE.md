# 🗄️ Data Management System User Guide

## Overview

The Wiz Aroma Food Delivery Data Management System provides comprehensive tools for managing system data, including seasonal resets, daily cleanup operations, and system monitoring. This guide covers all aspects of using the data management features safely and effectively.

## 🔐 Access Requirements

### Authorization

- **Authorized Users Only**: Access restricted to specific Telegram user IDs
- **Management Bot Required**: All operations performed through the management bot
- **Multi-Step Confirmation**: Critical operations require typed confirmation

### Security Features

- User ID verification for all reset operations
- Comprehensive audit logging of all actions
- Session validation and timeout protection
- Automatic backup creation before destructive operations

## 🔄 Seasonal Data Reset

### Purpose

Complete system refresh for new operational periods while preserving critical business data.

### What Gets Reset

- ✅ **Analytics Data**: All completed orders and performance metrics
- ✅ **Financial Records**: Revenue tracking and profit history
- ✅ **Personnel Earnings**: Individual delivery personnel earnings
- ✅ **Performance Statistics**: Delivery completion rates and rankings
- ✅ **Transaction Analytics**: Order volume data and growth metrics

### What Gets Preserved

- 🔒 **Personnel Profiles**: All delivery personnel records and contact information
- 🔒 **System Configuration**: Areas, cafes, menus, delivery locations
- 🔒 **User Accounts**: Customer profiles and preferences
- 🔒 **Current Orders**: Orders from the current day
- 🔒 **System Settings**: Bot configurations and operational parameters

### Step-by-Step Process

#### 1. Access System Management

```
Management Bot → 🔧 System Management → 🔄 Reset Season Data
```

#### 2. Review Warning Dialog

- System displays comprehensive data summary
- Shows exactly what will be reset vs. preserved
- Provides backup information and recovery options

#### 3. Confirm Understanding

- Click "⚠️ I Understand - Continue" to proceed
- Review final warning with consequences

#### 4. Type Confirmation Phrase

- Must type exactly: `CONFIRM SEASON RESET`
- Case-sensitive and space-sensitive
- No alternative phrases accepted

#### 5. Monitor Progress

- Real-time progress updates during execution
- Step-by-step status messages
- Completion confirmation with results summary

### Backup and Recovery

- **Automatic Backup**: Created before any data is deleted
- **24-Hour Recovery**: Full data restoration available
- **Timestamped Archives**: Organized by date and operation type
- **Audit Trail**: Complete log of all operations

## 🧹 Daily Order Cleanup

### Purpose

Regular maintenance to remove stale orders and optimize system performance.

### Cleanup Options

#### Standard Cleanup (24 hours)

- Removes orders older than 24 hours
- Archives incomplete deliveries
- Cleans failed assignments
- Preserves current day operations

#### Quick Cleanup (2 hours)

- More aggressive cleanup for end-of-day operations
- Removes orders older than 2 hours
- Frees up personnel assignments
- Optimizes for new order processing

### What Gets Cleaned

- **Stale Orders**: Confirmed but not completed orders beyond threshold
- **Failed Assignments**: Delivery assignments with failed status
- **Abandoned Orders**: Orders stuck in processing state
- **Orphaned Data**: Related cache and temporary tracking data

### Access and Execution

```
Management Bot → 🔧 System Management → 🧹 Daily Cleanup
```

1. **Choose Cleanup Type**: Standard (24h) or Quick (2h)
2. **Review Status**: Current order analysis and recommendations
3. **Execute Cleanup**: Automatic processing with progress updates
4. **View Results**: Summary of cleaned data and system optimization

## 📊 System Monitoring

### System Status Dashboard

- **Health Indicators**: Overall system status and alerts
- **Order Analysis**: Current order breakdown by age and status
- **Personnel Status**: Delivery staff availability and workload
- **Performance Metrics**: Response times and success rates

### Audit Log

- **Operation History**: Complete log of all reset and cleanup operations
- **User Tracking**: Who performed what operations and when
- **Status Monitoring**: Success/failure status of all operations
- **Detailed Results**: Specific data about what was affected

### Order Status Overview

- **Today's Activity**: Current day order statistics
- **Age Analysis**: Orders categorized by processing time
- **Assignment Status**: Active and failed delivery assignments
- **Cleanup Recommendations**: Automated suggestions for maintenance

## ⚠️ Safety Guidelines

### Before Performing Resets

1. **Verify Timing**: Ensure no active orders or critical operations
2. **Backup Verification**: Confirm backup systems are operational
3. **Team Notification**: Inform relevant team members
4. **Documentation**: Record the reason for reset in audit notes

### During Operations

1. **Monitor Progress**: Watch for error messages or failures
2. **Stay Available**: Remain accessible during the operation
3. **Don't Interrupt**: Allow operations to complete fully
4. **Document Issues**: Note any problems for future reference

### After Completion

1. **Verify Results**: Check that expected data was reset/preserved
2. **Test Functionality**: Ensure all systems work correctly
3. **Update Team**: Confirm successful completion
4. **Monitor Performance**: Watch for any post-reset issues

## 🚨 Emergency Procedures

### If Reset Fails Mid-Process

1. **Don't Panic**: Partial resets are recoverable
2. **Check Audit Log**: Review what was completed
3. **Contact Support**: Report the issue immediately
4. **Preserve State**: Don't attempt additional operations

### Data Recovery

1. **Access Backup**: Use timestamped backup archives
2. **Selective Restore**: Restore only necessary data
3. **Verify Integrity**: Check restored data completeness
4. **Test Systems**: Ensure full functionality after recovery

### System Issues

1. **Check Status**: Use System Status dashboard
2. **Review Logs**: Check audit log for recent operations
3. **Safe Mode**: Use read-only operations until resolved
4. **Professional Help**: Contact system administrator if needed

## 📞 Support and Troubleshooting

### Common Issues

- **Access Denied**: Verify user ID authorization
- **Confirmation Failed**: Check exact phrase spelling
- **Operation Timeout**: Large datasets may take longer
- **Display Errors**: Refresh and try again

### Getting Help

- **Audit Log**: Check for error details
- **System Status**: Review current system health
- **Documentation**: Refer to this guide
- **Technical Support**: Contact system administrator

### Best Practices

- **Regular Cleanup**: Perform daily cleanup during low-traffic periods
- **Seasonal Planning**: Schedule resets during maintenance windows
- **Backup Verification**: Regularly test backup and recovery procedures
- **Team Training**: Ensure all authorized users understand procedures

---

**⚠️ Important**: This system manages critical business data. Always follow proper procedures and verify operations before execution. When in doubt, consult with technical support before proceeding.
