"""
Data migration script for moving data from JSON files to Firebase.
This script takes the existing JSON files and migrates the data to Firebase Realtime Database.
"""

import os
import json
import argparse
import logging
from dotenv import load_dotenv
import sys

# Add the root directory to the path so we can import the modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.config import (
    POINTS_FILE,
    ORDER_HISTORY_FILE,
    USER_NAMES_FILE,
    USER_PHONE_NUMBERS_FILE,
    USER_EMAILS_FILE,
    FAVORITE_ORDERS_FILE,
    CURRENT_ORDERS_FILE,
    ORDER_STATUS_FILE,
    PENDING_ADMIN_REVIEWS_FILE,
    ADMIN_REMARKS_FILE,
    AWAITING_RECEIPT_FILE,
    AREAS_FILE,
    RESTAURANTS_FILE,
    MENUS_FILE,
    DELIVERY_LOCATIONS_FILE,
    DELIVERY_FEES_FILE,
    DATA_DIR,
)
from src.firebase_db import (
    initialize_firebase,
    migrate_data_to_firebase,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(), logging.FileHandler("migration.log")],
)
logger = logging.getLogger(__name__)


def load_json_file(file_path):
    """Load a JSON file and return its contents"""
    try:
        if os.path.exists(file_path):
            with open(file_path, "r", encoding="utf-8", errors="replace") as f:
                data = json.load(f)
            logger.info(f"Loaded data from {file_path}")
            return data
        else:
            logger.warning(f"File not found: {file_path}")
            return {}
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON from {file_path}: {e}")
        return {}
    except Exception as e:
        logger.error(f"Error loading file {file_path}: {e}")
        return {}


def migrate_all_data():
    """Migrate all data from JSON files to Firebase"""
    # Check if Firebase is initialized
    if not initialize_firebase():
        logger.error("Failed to initialize Firebase. Aborting migration.")
        return False

    # Define all data files to migrate
    migration_tasks = [
        {
            "file_path": POINTS_FILE,
            "firebase_path": "/user_points",
            "name": "User Points",
        },
        {
            "file_path": ORDER_HISTORY_FILE,
            "firebase_path": "/user_order_history",
            "name": "Order History",
        },
        {
            "file_path": USER_NAMES_FILE,
            "firebase_path": "/user_names",
            "name": "User Names",
        },
        {
            "file_path": USER_PHONE_NUMBERS_FILE,
            "firebase_path": "/user_phone_numbers",
            "name": "User Phone Numbers",
        },
        {
            "file_path": USER_EMAILS_FILE,
            "firebase_path": "/user_emails",
            "name": "User Emails",
        },
        {
            "file_path": FAVORITE_ORDERS_FILE,
            "firebase_path": "/favorite_orders",
            "name": "Favorite Orders",
        },
        {
            "file_path": CURRENT_ORDERS_FILE,
            "firebase_path": "/current_orders",
            "name": "Current Orders",
        },
        {
            "file_path": ORDER_STATUS_FILE,
            "firebase_path": "/order_status",
            "name": "Order Status",
        },
        {
            "file_path": PENDING_ADMIN_REVIEWS_FILE,
            "firebase_path": "/pending_admin_reviews",
            "name": "Pending Admin Reviews",
        },
        {
            "file_path": ADMIN_REMARKS_FILE,
            "firebase_path": "/admin_remarks",
            "name": "Admin Remarks",
        },
        {
            "file_path": AWAITING_RECEIPT_FILE,
            "firebase_path": "/awaiting_receipt",
            "name": "Awaiting Receipt",
        },
        {"file_path": AREAS_FILE, "firebase_path": "/areas", "name": "Areas"},
        {
            "file_path": RESTAURANTS_FILE,
            "firebase_path": "/restaurants",
            "name": "Restaurants",
        },
        {"file_path": MENUS_FILE, "firebase_path": "/menus", "name": "Menus"},
        {
            "file_path": DELIVERY_LOCATIONS_FILE,
            "firebase_path": "/delivery_locations",
            "name": "Delivery Locations",
        },
        {
            "file_path": DELIVERY_FEES_FILE,
            "firebase_path": "/delivery_fees",
            "name": "Delivery Fees",
        },
    ]

    # Migrate each file
    success_count = 0
    for task in migration_tasks:
        logger.info(
            f"Migrating {task['name']} from {task['file_path']} to Firebase {task['firebase_path']}"
        )
        data = load_json_file(task["file_path"])
        if data:  # Only migrate if we have data
            if migrate_data_to_firebase(data, task["firebase_path"]):
                logger.info(f"✓ Successfully migrated {task['name']} to Firebase")
                success_count += 1
            else:
                logger.error(f"✗ Failed to migrate {task['name']} to Firebase")
        else:
            logger.warning(f"⚠ No data to migrate for {task['name']}")

    # Report results
    logger.info(
        f"Migration complete. Successfully migrated {success_count}/{len(migration_tasks)} data sets."
    )
    return success_count == len(migration_tasks)


def migrate_single_file(file_path, firebase_path, name):
    """Migrate a single file to Firebase"""
    # Check if Firebase is initialized
    if not initialize_firebase():
        logger.error("Failed to initialize Firebase. Aborting migration.")
        return False

    logger.info(f"Migrating {name} from {file_path} to Firebase {firebase_path}")
    data = load_json_file(file_path)
    if data:  # Only migrate if we have data
        if migrate_data_to_firebase(data, firebase_path):
            logger.info(f"✓ Successfully migrated {name} to Firebase")
            return True
        else:
            logger.error(f"✗ Failed to migrate {name} to Firebase")
            return False
    else:
        logger.warning(f"⚠ No data to migrate for {name}")
        return False


def verify_data_dir():
    """Verify the data directory exists"""
    if not os.path.exists(DATA_DIR):
        logger.error(f"Data directory not found: {DATA_DIR}")
        logger.info(f"Creating data directory: {DATA_DIR}")
        os.makedirs(DATA_DIR, exist_ok=True)


def main():
    """Main function to run the migration"""
    # Load environment variables
    load_dotenv()

    # Verify data directory
    verify_data_dir()

    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Migrate data from JSON files to Firebase"
    )
    parser.add_argument("--file", help="Migrate only a specific file")
    parser.add_argument("--path", help="Firebase path for the specific file")
    parser.add_argument("--name", help="Name of the data being migrated")
    args = parser.parse_args()

    # Check if specific file is provided
    if args.file:
        if not args.path or not args.name:
            logger.error(
                "When specifying a file, you must also provide --path and --name"
            )
            return False
        return migrate_single_file(args.file, args.path, args.name)
    else:
        return migrate_all_data()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
