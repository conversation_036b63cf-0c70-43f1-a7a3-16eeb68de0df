# 🔐 Security Setup Guide for Wiz Aroma Food Delivery

## ⚠️ CRITICAL SECURITY NOTICE

This guide contains instructions for setting up the Wiz Aroma Food Delivery project securely. **NEVER commit sensitive credentials to the repository.**

## 🚨 Immediate Actions Required

### 1. Environment Variables Setup

Create a `.env` file in the project root with the following structure:

```bash
# Bot Tokens (Get these from @BotFather on Telegram)
BOT_TOKEN=your_main_bot_token_here
ADMIN_BOT_TOKEN=your_admin_bot_token_here
FINANCE_BOT_TOKEN=your_finance_bot_token_here
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token_here
MANAGEMENT_BOT_TOKEN=your_management_bot_token_here
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token_here
DELIVERY_BOT_TOKEN=your_delivery_bot_token_here

# Chat IDs (Get these from your Telegram account)
ADMIN_CHAT_IDS="[your_admin_telegram_id]"
FINANCE_CHAT_ID=your_finance_telegram_id
MAINTENANCE_CHAT_ID=your_maintenance_telegram_id
MANAGEMENT_CHAT_ID=your_management_telegram_id

# Email Configuration (Use app-specific passwords)
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_specific_password

# Payment Information
TELEBIRR_PHONE=your_telebirr_phone
TELEBIRR_NAME=your_telebirr_name
CBE_ACCOUNT_NUMBER=your_cbe_account
CBE_ACCOUNT_NAME=your_cbe_name
BOA_ACCOUNT_NUMBER=your_boa_account
BOA_ACCOUNT_NAME=your_boa_name

# Contact Information
SUPPORT_PHONE_1=your_support_phone_1
SUPPORT_PHONE_2=your_support_phone_2
SUPPORT_TELEGRAM=@your_support_handle

# Firebase Configuration
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com
FIREBASE_CREDENTIALS_PATH=path/to/your/firebase-credentials.json
```

### 2. Firebase Service Account Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to Project Settings → Service Accounts
4. Click "Generate new private key"
5. Save the JSON file outside the repository (e.g., in a `secrets/` folder)
6. Update `FIREBASE_CREDENTIALS_PATH` in your `.env` file

### 3. Security Checklist

- [ ] `.env` file created and configured
- [ ] Firebase credentials file stored outside repository
- [ ] All bot tokens obtained from @BotFather
- [ ] Chat IDs configured for authorized users
- [ ] Email app-specific password configured
- [ ] Payment account information configured
- [ ] `.env` file added to `.gitignore` (already done)
- [ ] Firebase credentials file path excluded from git

## 🔒 Security Best Practices

### Token Management

- **NEVER** commit tokens to git
- Rotate tokens regularly (monthly recommended)
- Use different tokens for development and production
- Monitor token usage in Telegram Bot API logs

### Firebase Security

- Use service account keys with minimal required permissions
- Enable Firebase Authentication for production
- Configure Firestore security rules (see FIREBASE_SECURITY_RULES.md)
- Monitor Firebase usage and access logs

### Access Control

- Limit admin chat IDs to trusted personnel only
- Use strong, unique passwords for email accounts
- Enable 2FA on all service accounts
- Regularly audit user access and permissions

## 🚨 Emergency Procedures

### If Credentials Are Compromised

1. **Immediately** revoke the compromised tokens/keys
2. Generate new credentials
3. Update `.env` file with new credentials
4. Restart all bot services
5. Monitor for unauthorized access
6. Report incident to team lead

### If Repository Contains Sensitive Data

1. **DO NOT** push to remote repository
2. Remove sensitive files immediately
3. Use `git filter-branch` or BFG Repo-Cleaner to remove from history
4. Force push cleaned repository
5. Notify all team members to re-clone

## 📞 Security Contact

For security issues or questions:

- Primary: [Your Security Contact]
- Secondary: [Backup Contact]
- Emergency: [Emergency Contact]

## 🔄 Regular Security Tasks

### Weekly

- [ ] Review access logs
- [ ] Check for unauthorized access attempts
- [ ] Verify bot functionality

### Monthly

- [ ] Rotate API tokens
- [ ] Update Firebase security rules
- [ ] Review user permissions
- [ ] Security audit of new code changes

### Quarterly

- [ ] Full security assessment
- [ ] Update security documentation
- [ ] Review and update emergency procedures
- [ ] Security training for team members
