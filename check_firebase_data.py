#!/usr/bin/env python3
"""
<PERSON>ript to check Firebase data and verify sample menu data is properly stored
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Check Firebase data"""
    try:
        from firebase_db import initialize_firebase, get_data
        
        print("🔍 Checking Firebase Data...")
        print("=" * 50)
        
        if not initialize_firebase():
            print("❌ Failed to initialize Firebase")
            return False
        
        print("✅ Firebase initialized successfully")
        print()
        
        # Check menus data
        print("📋 Checking Menus Data:")
        menus_data = get_data("menus")
        if menus_data:
            print(f"✅ Menus data found: {type(menus_data)}")
            
            print(f"📋 Menus data structure: {menus_data}")

            if "restaurant_menus" in menus_data:
                restaurant_menus = menus_data["restaurant_menus"]
                print(f"✅ Restaurant menus found: {type(restaurant_menus)}")
                print(f"📋 Restaurant menus content: {restaurant_menus}")

                if isinstance(restaurant_menus, dict):
                    for restaurant_id, menu_items in restaurant_menus.items():
                        print(f"   Restaurant {restaurant_id}: {len(menu_items)} items")
                        if menu_items:
                            print(f"      First item: {menu_items[0]}")
                else:
                    print(f"❌ Restaurant menus is not a dict: {type(restaurant_menus)}")
            else:
                print("❌ No restaurant_menus found in menus data")
        else:
            print("❌ No menus data found in Firebase")
        
        print()
        
        # Check areas data
        print("📍 Checking Areas Data:")
        areas_data = get_data("areas")
        if areas_data and "areas" in areas_data:
            areas = areas_data["areas"]
            print(f"✅ Areas found: {len(areas)} areas")
            for area in areas:
                print(f"   - {area}")
        else:
            print("❌ No areas data found")
        
        print()
        
        # Check restaurants data
        print("🏪 Checking Restaurants Data:")
        restaurants_data = get_data("restaurants")
        if restaurants_data and "restaurants" in restaurants_data:
            restaurants = restaurants_data["restaurants"]
            print(f"✅ Restaurants found: {len(restaurants)} restaurants")
            for restaurant in restaurants:
                print(f"   - {restaurant}")
        else:
            print("❌ No restaurants data found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Firebase data: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
