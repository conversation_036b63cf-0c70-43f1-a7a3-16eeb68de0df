# Wiz Aroma Food Delivery Bot Setup Guide

This guide will help you set up the Telegram bots required for the Wiz Aroma Food Delivery system.

## Creating Bot Tokens

You need to create four separate bots for the system to work properly:

1. **User Bot**: The main bot that customers interact with
2. **Admin Bot**: For administrators to manage orders
3. **Finance Bot**: For finance team to verify payments
4. **Maintenance Bot**: For system maintenance and configuration

### Steps to Create Bot Tokens

1. Open Telegram and search for @BotFather
2. Start a chat with BotFather
3. Create each bot using the `/newbot` command:

   For the main user bot:

   ```
   /newbot
   Wiz Aroma User Bot
   wiz_aroma_user_bot (or any available username)
   ```

   For the admin bot:

   ```
   /newbot
   Wiz Aroma Admin Bot
   wiz_aroma_admin_bot (or any available username)
   ```

   For the finance bot:

   ```
   /newbot
   Wiz Aroma Finance Bot
   wiz_aroma_finance_bot (or any available username)
   ```

   For the maintenance bot:

   ```
   /newbot
   Wiz Aroma Maintenance Bot
   wiz_aroma_maintenance_bot (or any available username)
   ```

4. <PERSON><PERSON><PERSON><PERSON> will give you a token for each bot. Save these tokens.

## Configuring the .env File

1. Open the `.env` file in the root directory of the project
2. Update the bot tokens with the ones you received from BotFather:

```
# Bot Tokens
BOT_TOKEN=your_user_bot_token_here
ADMIN_BOT_TOKEN=your_admin_bot_token_here
FINANCE_BOT_TOKEN=your_finance_bot_token_here
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token_here
```

3. Make sure the Telegram ID for admin, finance, and maintenance access is correct:

```
# Chat IDs
ADMIN_CHAT_IDS="[your_telegram_id]"
FINANCE_CHAT_ID=your_telegram_id
MAINTENANCE_CHAT_ID=your_telegram_id
```

## Running the Bots

It's recommended to run the bots individually to avoid conflicts:

- To run the user bot: `python main.py --bot user`
- To run the admin bot: `python main.py --bot admin`
- To run the finance bot: `python main.py --bot finance`
- To run the maintenance bot: `python main.py --bot maintenance`

Running all bots at once can cause conflicts if they're not properly configured with different tokens:

- To run all bots: `python main.py --bot all`

## Troubleshooting

If you encounter the error "Conflict: terminated by other getUpdates request":

1. Make sure each bot has a unique token in the `.env` file
2. Stop all running bot instances
3. Run the bots individually instead of all at once
4. If the error persists, try restarting your computer and running the bots again

## Additional Bot Configuration

After creating your bots, you should configure them properly:

1. Set descriptions using `/setdescription` in BotFather
2. Set profile pictures using `/setuserpic` in BotFather
3. Set command lists using `/setcommands` in BotFather
4. For admin, finance, and maintenance bots, turn off privacy mode using `/setprivacy` in BotFather
